import { createWorker } from 'tesseract.js';
import { OCRResult, OCRConfig, MedicineInfo, MedicineIdentificationResult } from '@/types/ocr';

/**
 * Medical OCR class for extracting text from medicine images using Tesseract.js
 */
export class MedicalOCR {
  private worker: any | null = null;
  private isInitialized = false;
  private config: OCRConfig;

  constructor(config: OCRConfig = {}) {
    this.config = {
      language: 'eng',
      minConfidence: 60,
      debug: false,
      tesseractOptions: {
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 .-',
        tessedit_pageseg_mode: '6', // PSM.SINGLE_BLOCK
      },
      ...config,
    };
  }

  /**
   * Initialize the Tesseract worker
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🔄 Initializing Tesseract OCR worker...');

      // Create worker using Tesseract.js v6 API - createWorker returns a Promise and is already initialized
      this.worker = await createWorker(this.config.language || 'eng');

      console.log('🔄 Worker created and initialized successfully');

      // Set custom parameters if provided
      if (this.config.tesseractOptions) {
        console.log('🔄 Setting custom parameters...');
        await this.worker.setParameters(this.config.tesseractOptions);
      }

      this.isInitialized = true;
      console.log('✅ Tesseract OCR worker ready for use');
    } catch (error) {
      console.error('❌ Failed to initialize Tesseract worker:', error);
      this.worker = null;
      this.isInitialized = false;
      throw new Error(`OCR initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract text from an image file
   */
  async extractText(imageFile: File): Promise<OCRResult> {
    if (!this.isInitialized || !this.worker) {
      await this.initialize();
    }

    const startTime = Date.now();

    try {
      console.log(`🔄 Starting OCR processing for: ${imageFile.name}`);
      
      const { data } = await this.worker!.recognize(imageFile);
      const processingTime = Date.now() - startTime;

      const result: OCRResult = {
        text: data.text.trim(),
        confidence: data.confidence,
        processingTime,
        words: data.words || [],
        success: true,
      };

      console.log(`✅ OCR completed in ${processingTime}ms with ${data.confidence.toFixed(1)}% confidence`);
      
      if (this.config.debug) {
        console.log('📝 Extracted text:', result.text);
        console.log('📊 Word details:', result.words);
      }

      return result;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown OCR error';
      
      console.error('❌ OCR processing failed:', errorMessage);

      return {
        text: '',
        confidence: 0,
        processingTime,
        words: [],
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Extract medicine-specific information from OCR text
   */
  extractMedicineInfo(ocrText: string): MedicineInfo {
    const cleanText = this.cleanText(ocrText);
    const potentialNames = this.extractPotentialMedicineNames(cleanText);
    const dosageInfo = this.extractDosageInfo(cleanText);
    const medicineType = this.identifyMedicineType(cleanText);

    return {
      potentialNames,
      dosageInfo,
      medicineType,
      confidence: this.calculateMedicineConfidence(potentialNames, dosageInfo),
      rawText: ocrText,
      cleanedText: cleanText,
    };
  }

  /**
   * Complete medicine identification process
   */
  async identifyMedicine(imageFile: File): Promise<MedicineIdentificationResult> {
    try {
      console.log('🚀 Starting complete medicine identification process...');
      
      // Step 1: Extract text using OCR
      const ocrResult = await this.extractText(imageFile);
      
      if (!ocrResult.success) {
        return {
          ocrResult,
          medicineInfo: this.getEmptyMedicineInfo(),
          success: false,
        };
      }

      // Step 2: Extract medicine-specific information
      const medicineInfo = this.extractMedicineInfo(ocrResult.text);
      
      // Step 3: Identify the most likely medicine
      const identifiedMedicine = this.selectBestMedicine(medicineInfo.potentialNames);

      const result: MedicineIdentificationResult = {
        ocrResult,
        medicineInfo,
        identifiedMedicine,
        success: ocrResult.success && medicineInfo.confidence >= (this.config.minConfidence || 60),
      };

      console.log(`✅ Medicine identification completed. Success: ${result.success}`);
      if (result.identifiedMedicine) {
        console.log(`🎯 Identified medicine: ${result.identifiedMedicine}`);
      }

      return result;
    } catch (error) {
      console.error('❌ Medicine identification failed:', error);
      
      return {
        ocrResult: {
          text: '',
          confidence: 0,
          processingTime: 0,
          words: [],
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        medicineInfo: this.getEmptyMedicineInfo(),
        success: false,
      };
    }
  }

  /**
   * Clean and normalize text for better processing
   */
  private cleanText(text: string): string {
    return text
      .replace(/[^\w\s\d.-]/g, ' ') // Remove special characters except dots and hyphens
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .toLowerCase();
  }

  /**
   * Extract potential medicine names from cleaned text
   */
  private extractPotentialMedicineNames(cleanText: string): string[] {
    const words = cleanText.split(' ');
    const potentialNames: string[] = [];

    // Look for medicine-like patterns
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      
      // Skip very short words
      if (word.length < 3) continue;
      
      // Look for medicine name patterns
      if (this.isMedicineNamePattern(word)) {
        potentialNames.push(word);
        
        // Check for compound names (e.g., "aspirin extra")
        if (i + 1 < words.length && this.isMedicineModifier(words[i + 1])) {
          potentialNames.push(`${word} ${words[i + 1]}`);
        }
      }
    }

    return [...new Set(potentialNames)]; // Remove duplicates
  }

  /**
   * Check if a word matches medicine name patterns
   */
  private isMedicineNamePattern(word: string): boolean {
    // Common medicine name endings
    const medicineEndings = ['in', 'ol', 'an', 'ex', 'ine', 'ate', 'ide'];
    
    // Check for common medicine name patterns
    return (
      word.length >= 4 &&
      (medicineEndings.some(ending => word.endsWith(ending)) ||
       word.includes('aspirin') ||
       word.includes('tylenol') ||
       word.includes('advil') ||
       word.includes('panadol') ||
       word.includes('amoxicillin') ||
       word.includes('ibuprofen'))
    );
  }

  /**
   * Check if a word is a medicine modifier (strength, type, etc.)
   */
  private isMedicineModifier(word: string): boolean {
    const modifiers = ['extra', 'strength', 'forte', 'plus', 'max', 'rapid', 'extended'];
    return modifiers.includes(word);
  }

  /**
   * Extract dosage information from text
   */
  private extractDosageInfo(cleanText: string): string | undefined {
    const dosagePatterns = [
      /(\d+)\s*(mg|g|ml|mcg|iu)/gi,
      /(\d+)\s*(milligram|gram|milliliter|microgram)/gi,
    ];

    for (const pattern of dosagePatterns) {
      const match = cleanText.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return undefined;
  }

  /**
   * Identify medicine type/category
   */
  private identifyMedicineType(cleanText: string): string | undefined {
    const typeKeywords = {
      'pain relief': ['pain', 'relief', 'analgesic', 'aspirin', 'ibuprofen', 'tylenol'],
      'antibiotic': ['antibiotic', 'amoxicillin', 'penicillin', 'azithromycin'],
      'vitamin': ['vitamin', 'supplement', 'multivitamin'],
      'allergy': ['allergy', 'antihistamine', 'benadryl', 'claritin'],
    };

    for (const [type, keywords] of Object.entries(typeKeywords)) {
      if (keywords.some(keyword => cleanText.includes(keyword))) {
        return type;
      }
    }

    return undefined;
  }

  /**
   * Calculate confidence score for medicine identification
   */
  private calculateMedicineConfidence(potentialNames: string[], dosageInfo?: string): number {
    let confidence = 0;

    // Base confidence from having potential names
    if (potentialNames.length > 0) {
      confidence += 40;
    }

    // Bonus for multiple potential names
    confidence += Math.min(potentialNames.length * 10, 30);

    // Bonus for having dosage information
    if (dosageInfo) {
      confidence += 20;
    }

    // Bonus for recognizable medicine names
    const recognizableNames = potentialNames.filter(name => 
      ['aspirin', 'tylenol', 'advil', 'ibuprofen', 'amoxicillin', 'panadol'].some(known => 
        name.includes(known)
      )
    );
    confidence += recognizableNames.length * 15;

    return Math.min(confidence, 100);
  }

  /**
   * Select the best medicine from potential names
   */
  private selectBestMedicine(potentialNames: string[]): string | undefined {
    if (potentialNames.length === 0) return undefined;

    // Prioritize longer, more specific names
    return potentialNames.sort((a, b) => b.length - a.length)[0];
  }

  /**
   * Get empty medicine info structure
   */
  private getEmptyMedicineInfo(): MedicineInfo {
    return {
      potentialNames: [],
      confidence: 0,
      rawText: '',
      cleanedText: '',
    };
  }

  /**
   * Cleanup resources
   */
  async terminate(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
      console.log('🔄 Tesseract worker terminated');
    }
  }
}
